import { Body, Controller, Post, UseGuards, Req, Get } from '@nestjs/common';
import { AuthService } from './auth.service';
import { IAuth } from './interfaces/auth.interfaces';
import { signInDto } from './dto/signIn.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiUnauthorizedResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthResponseDto } from './dto/auth-response.dto';
import {
  ErrorResponseDto,
  ValidationErrorResponseDto,
  UnauthorizedErrorResponseDto,
} from '../common/dto/error-response.dto';
import { ForgotPasswordDto } from './dto/forget-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { VerifyOtpDTO } from './dto/verify-otp.dto';
import { AuthGuard } from './auth.guard';
import { signUpDto } from './dto/signUp.dto';
import { ResendVerificationOtpDto } from './dto/resend-verification-otp.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  
  @Post('signup')
  @ApiOperation({
    summary: 'User registration',
    description:
      'Register a new user account and receive an authentication token',
  })
  @ApiBody({
    type: signUpDto,
    description: 'User registration data',
  })
  @ApiResponse({
    status: 201,
    description:
      'User registered successfully, OTP sent for email verification',
    schema: {
      example: {
        message:
          'Verification OTP <NAME_EMAIL>. Please verify your email to complete registration.',
        email: '<EMAIL>',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Validation error or invalid input data',
    type: ValidationErrorResponseDto,
  })
  @ApiConflictResponse({
    description: 'User already exists with this email',
    type: ErrorResponseDto,
    schema: {
      example: {
        statusCode: 409,
        message: 'User already exists',
        error: 'Conflict',
        timestamp: '2024-01-15T10:30:00.000Z',
        path: '/api/auth/signup',
      },
    },
  })
  async signUp(
    @Body() signUpDto: signUpDto,
  ): Promise<{ message: string; email: string }> {
    return this.authService.signUp(signUpDto);
  }

  @Post('signin')
  @ApiOperation({
    summary: 'User login',
    description:
      'Authenticate user credentials and receive an authentication token. Email must be verified.',
  })
  @ApiBody({
    type: signInDto,
    description: 'User login credentials',
  })
  @ApiResponse({
    status: 200,
    description: 'User authenticated successfully',
    type: AuthResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error or invalid input data',
    type: ValidationErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid credentials or email not verified',
    type: UnauthorizedErrorResponseDto,
    schema: {
      example: {
        statusCode: 401,
        message: 'Please verify your email before signing in',
        code: 'EMAIL_NOT_VERIFIED',
        timestamp: '2024-01-15T10:30:00.000Z',
        path: '/api/auth/signin',
      },
    },
  })
  async signIn(@Body() signInDto: signInDto): Promise<IAuth> {
    return this.authService.signIn(signInDto);
  }

  @Post('forgot-password')
  @ApiOperation({
    summary: 'Forgot password',
    description: 'Send OTP to reset password',
  })
  @ApiBody({
    type: ForgotPasswordDto,
    description: 'User email for OTP',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP sent successfully',
    schema: {
      example: {
        message: 'OTP <NAME_EMAIL>',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid email',
    type: ErrorResponseDto,
  })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post('verify-otp')
  @ApiOperation({
    summary: 'Verify OTP',
    description: 'Verify OTP sent for password reset',
  })
  @ApiBody({
    type: VerifyOtpDTO,
    description: 'OTP and email',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP verified successfully',
    schema: {
      example: {
        message: 'OTP verified',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid OTP',
    type: ErrorResponseDto,
  })
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDTO) {
    return this.authService.verifyOtp(verifyOtpDto);
  }

  @Post('verify-email-otp')
  @ApiOperation({
    summary: 'Verify Email OTP',
    description: 'Verify OTP sent for email verification during signup',
  })
  @ApiBody({
    type: VerifyOtpDTO,
    description: 'OTP and email for verification',
  })
  @ApiResponse({
    status: 200,
    description: 'Email verified successfully, user registration completed',
    type: AuthResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid or expired OTP',
    type: ErrorResponseDto,
  })
  async verifyEmailOtp(@Body() verifyOtpDto: VerifyOtpDTO) {
    return this.authService.verifyEmailOtp(verifyOtpDto);
  }

  @Post('resend-verification-otp')
  @ApiOperation({
    summary: 'Resend Email Verification OTP',
    description: 'Resend OTP for email verification',
  })
  @ApiBody({
    type: ResendVerificationOtpDto,
    description: 'Email address to resend OTP',
  })
  @ApiResponse({
    status: 200,
    description: 'Verification OTP resent successfully',
    schema: {
      example: {
        message: 'Verification OTP <NAME_EMAIL>',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'User not found or email already verified',
    type: ErrorResponseDto,
  })
  async resendVerificationOtp(
    @Body() resendVerificationOtpDto: ResendVerificationOtpDto,
  ) {
    return this.authService.resendVerificationOtp(
      resendVerificationOtpDto.email,
    );
  }

  @Post('reset-password')
  @ApiOperation({
    summary: 'Reset password',
    description: 'Reset password using OTP',
  })
  @ApiBody({
    type: ResetPasswordDto,
    description: 'New password and email',
  })
  @ApiResponse({
    status: 200,
    description: 'Password reset successfully',
    schema: {
      example: {
        message: 'PASSWORD RESTORED - 200',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid email',
    type: ErrorResponseDto,
  })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @UseGuards(AuthGuard)
  @Post('change-password')
  @ApiBearerAuth('Bearer-auth')
  @ApiOperation({
    summary: 'Change password',
    description: 'Change password using old password',
  })
  @ApiBody({
    type: ChangePasswordDto,
    description: 'Old password and new password',
  })
  @ApiResponse({
    status: 200,
    description: 'Password changed successfully',
    schema: {
      example: {
        message: 'Password changed successfully',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Old password and new password are required',
    type: ErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid old password',
    type: UnauthorizedErrorResponseDto,
  })
  async changePassword(
    @Req() req,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    const userId = req.user._id;
    return this.authService.changePassword(userId, changePasswordDto);
  }


  @Post('google-mobile')
  @ApiOperation({
    summary: 'Google OAuth Login',
    description: 'Authenticate user with Google ID token and return JWT access token. Creates new user if not exists.',
  })
  @ApiBody({
    description: 'Google ID token for authentication',
    schema: {
      type: 'object',
      properties: {
        idToken: {
          type: 'string',
          description: 'Google ID token received from Google OAuth',
          example: 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjdkYzBiMjc...',
        },
      },
      required: ['idToken'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'User authenticated successfully with Google',
    schema: {
      type: 'object',
      properties: {
        accessToken: {
          type: 'string',
          description: 'JWT access token for API authentication',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        user: {
          type: 'object',
          description: 'User information',
          properties: {
            _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
            email: { type: 'string', example: '<EMAIL>' },
            firstName: { type: 'string', example: 'Muhmmad' },
            lastName: { type: 'string', example: 'Hassan' },
            photo: { type: 'string', example: 'https://lh3.googleusercontent.com/a/...' },
            googleId: { type: 'string', example: '105164865927623781547' },
            provider: { type: 'string', example: 'google' },
            isVerified: { type: 'boolean', example: true },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid Google ID token',
    type: ErrorResponseDto,
  })
  async googleLogin(@Body() body: { idToken: string }) {
    const { idToken } = body;
    const userData = await this.authService.verifyGoogleToken(idToken);
    console.log("Google user data:", userData);

    const user = await this.authService.findOrCreateUser(userData);

    // issue your JWT
    const jwt = await this.authService.generateJwt(user);

    return { accessToken: jwt, user };
  }

  @Post('facebook-mobile')
  @ApiOperation({
    summary: 'Facebook OAuth Login',
    description: 'Authenticate user with Facebook access token and return JWT access token. Creates new user if not exists.',
  })
  @ApiBody({
    description: 'Facebook access token for authentication',
    schema: {
      type: 'object',
      properties: {
        accessToken: {
          type: 'string',
          description: 'Facebook access token received from Facebook OAuth',
          example: 'EAABwzLixnjYBO...',
        },
      },
      required: ['accessToken'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'User authenticated successfully with Facebook',
    schema: {
      type: 'object',
      properties: {
        accessToken: {
          type: 'string',
          description: 'JWT access token for API authentication',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        user: {
          type: 'object',
          description: 'User information',
          properties: {
            _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
            email: { type: 'string', example: '<EMAIL>' },
            firstName: { type: 'string', example: 'Muhmmad' },
            lastName: { type: 'string', example: 'Hassan' },
            photo: { type: 'string', example: 'https://graph.facebook.com/v18.0/...' },
            facebookId: { type: 'string', example: '105164865927623781547' },
            provider: { type: 'string', example: 'facebook' },
            isVerified: { type: 'boolean', example: true },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid Facebook access token',
    type: ErrorResponseDto,
  })
  async facebookLogin(@Body() body: { accessToken: string }) {
    const { accessToken } = body;
    const userData = await this.authService.verifyFacebookToken(accessToken);
    console.log("Facebook user data:", userData);

    const user = await this.authService.handleFacebookLogin(userData);

    // issue your JWT
    const jwt = await this.authService.generateJwt(user);

    return { accessToken: jwt, user };
  }
}

