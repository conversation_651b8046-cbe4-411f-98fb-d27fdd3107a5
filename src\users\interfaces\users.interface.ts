export interface IUser {
  _id: string;
  firstName?: string;
  lastName?: string;
  email: string;
  password: string;
  mobileNumber?: string;
  gender?: string;
  dob?: string;
  isVerified?: boolean;
  healthAssessmentData?: IHealthAssessmentData;
  fcmToken?: string;
  completedStep?: number;
}

export interface IUserSignUp{
  _id:string;
  email:string;
  password:string;
}

export interface IGlycemicMonitoringData {
  hba1c?: string;
  fastingGlucose?: string;
  lastTestInterval?: string;
  hba1cNotRemembered?: boolean;
  lastTestMoreThanYear?: boolean;
}

export interface IBloodPressureData {
  systolic?: string;
  diastolic?: string;
  pulse?: string;
  bpNotRemembered?: boolean;
}

export interface ILifeCraftData {
  analyzing?: boolean;
  plan?: boolean;
}

export interface IRemindersData {
  bloodPressure?: boolean;
}

export interface IHealthAssessmentData {
  gender?: string;
  ageRange?: string;
  weight?: string;
  height?: string;
  bmi?: string;
  conditionsToManage?: string[];
  diabetesType?: string;
  diabetesDiagnosedSince?: string;
  medicationStatus?: string;
  medicationType?: string[];
  glycemicMonitoring?: IGlycemicMonitoringData;
  hypertensionStatus?: string;
  hypertensionSince?: string;
  bloodPressure?: IBloodPressureData;
  comorbidities?: string[];
  stressLevel?: string;
  emotion?: string;
  sleepQuality?: string;
  fallAsleep?: string;
  fallAsToNightleep?: string;
  diabetesLevel?: string;
  fitnessLevel?: string;
  lifeCraft?: ILifeCraftData;
  reminders?: IRemindersData;
}

export interface IHealthAssessment {
  ageRange?: string;
  height?: number;
  weight?: number;
  bmi?: number;
  bodyFatPercentage?: number;
  conditionsToManage?: string[];
}

export interface IDiabetesInfo {
  diabetesType?: string;
  diabetesDiagnosedSince?: string;
  takesMedication?: boolean;
  medications?: string[];
  recentHbA1c?: number;
  rememberHbA1c?: boolean;
  lastTestDuration?: string;
  recentFastingGlucose?: number;
  diabetesMotivationLevel?: number;
}

export interface IBloodPressureInfo {
  hyperTension?: boolean;
  diagonisedYear?: number;
  bpSystolic?: number;
  bpDiastolic?: number;
  heartRate?: number;
  rememberBp?: boolean;
}

export interface IMeasurementReminders {
  bpMeasurementReminders?: boolean;
  bloodSugarMeasurementReminders?: boolean;
}

export interface IMentalHealthInfo {
  comorbidities?: string[];
  anxietyLevel?: string;
  commonEmotions?: string[];
}

export interface ISleepInfo {
  sleepQualityRating?: string;
  troubleFallingAsleep?: string;
  sleepCondition?: string;
}

export interface IFitnessInfo {
  fitnessLevel?: string;
  motivationInsightsAccepted?: boolean;
}
