import { HttpException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Users } from './schema/users.schema';
import { IUser, IUserSignUp } from './interfaces/users.interface';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { CreateUserDto } from './dto/createUser.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
import { signUpDto } from 'src/auth/dto/signUp.dto';

@Injectable()
export class UsersService {
  constructor(@InjectModel(Users.name) private userModel: Model<IUser>) {}

  async createUser(createUserDto:signUpDto ): Promise<Omit<IUserSignUp, "password">> {
    try {
      const email = createUserDto.email.toLowerCase();
      const existingUser = await this.userModel.find({ email });
      if (existingUser.length) {
        throw new HttpException("User already exists", 400);
      }

   
      const salt = await bcrypt.genSalt();
      const hashedPassword = await bcrypt.hash(createUserDto.password, salt);

      const user = await this.userModel.create({
        email,
        password: hashedPassword
      });

      const { password, ...userWithoutPassword } = user.toObject();
      return { _id: user._id, email: userWithoutPassword.email };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to create user: ${error.message}`);
    }
  }

  async findByEmail(email: string): Promise<Omit<IUser, "password">> {
    try {
      const user = await this.userModel.findOne({ email }).exec();
      if (!user) {
        throw new HttpException('invalid credentials', 404);
      }
      const { password, ...userWithoutPassword } = user.toObject();
      return { ...userWithoutPassword };
    } catch (error) {
      throw new HttpException(`error: ${error.message}`, 404);
    }
  }

  async findByEmailWithPassword(email: string): Promise<IUser | null> {
    try {
      const user = await this.userModel.findOne({ email }).select('+password').exec();
      if (!user) {
        throw new HttpException('invalid credentials', 404);
      }
      return user;
    } catch (error) {
      throw new HttpException(`error: ${error.message}`, 404);
    }
  }

  async findUserById(id: string): Promise<Omit<IUser, "password">> {
    try {
      const user = await this.userModel.findById(id).exec();
      if (!user) {
        throw new HttpException('User not found', 404);
      }
      const { password, ...userWithoutPassword } = user.toObject();
      return { ...userWithoutPassword };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch user by ID: ${error.message}`);
    }
  }

  async findAllUsers(): Promise<Omit<IUser, "password">[]> {
    try {
      const users = await this.userModel.find().exec();
      return users.map((user) => {
        const { password, ...userWithoutPassword } = user.toObject();
        return userWithoutPassword;
      });
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch users: ${error.message}`);
    }
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto): Promise<Omit<IUser, "password">> {
    try {
      // Build the update object with proper nested field handling
      const updateObject: any = {};

      // Handle non-nested fields directly
      const { healthAssessmentData, ...directFields } = updateUserDto;
      Object.assign(updateObject, directFields);

      // Handle healthAssessmentData with dot notation to preserve existing nested data
      if (healthAssessmentData) {
        Object.keys(healthAssessmentData).forEach(key => {
          const value = healthAssessmentData[key as keyof typeof healthAssessmentData];
          if (value !== undefined) {
            // Handle nested objects within healthAssessmentData
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
              Object.keys(value).forEach(nestedKey => {
                const nestedValue = (value as any)[nestedKey];
                if (nestedValue !== undefined) {
                  updateObject[`healthAssessmentData.${key}.${nestedKey}`] = nestedValue;
                }
              });
            } else {
              updateObject[`healthAssessmentData.${key}`] = value;
            }
          }
        });
      }

      const user = await this.userModel.findByIdAndUpdate(
        id,
        { $set: updateObject },
        { new: true }
      ).exec();

      if (!user) {
        throw new HttpException('User not found', 404);
      }
      const { password, ...userWithoutPassword } = user.toObject();
      return { ...userWithoutPassword };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to update user: ${error.message}`);
    }
  }

  async update(email: string, updateData: Partial<Users>): Promise<IUser> {
    try {
      const updatedUser = await this.userModel
        .findOneAndUpdate({ email }, updateData, { new: true })
        .exec();
      if (!updatedUser) {
        throw new HttpException('User not found', 404);
      }
      return updatedUser;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to update user by email: ${error.message}`);
    }
  }

  async deleteUser(id: string): Promise<string> {
    try {
      const result = await this.userModel.findByIdAndDelete(id).exec();
      if (!result) {
        throw new HttpException('User not found', 404);
      }
      return 'User deleted';
    } catch (error) {
      throw new InternalServerErrorException(`Failed to delete user: ${error.message}`);
    }
  }
}
