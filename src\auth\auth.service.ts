import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from 'src/users/users.service';
import { IAuth, ISignUp } from './interfaces/auth.interfaces';
import { IUser, IUserSignUp } from 'src/users/interfaces/users.interface';
import { CreateUserDto } from 'src/users/dto/createUser.dto';
import { signInDto } from './dto/signIn.dto';
import * as bcrypt from 'bcrypt';
import { OtpService } from 'src/otp/otp.service';
import { otpgenerator } from 'src/ultils/otpHelper';
import { ForgotPasswordDto } from './dto/forget-password.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { CreateOtpDto } from '../otp/dto/create-otp.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Users } from 'src/users/schema/users.schema';
import { Model } from 'mongoose';
import { VerifyOtpDTO } from './dto/verify-otp.dto';
import { signUpDto } from './dto/signUp.dto';
import { OAuth2Client } from 'google-auth-library';

@Injectable()
export class AuthService {
  private client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
  constructor(
    @InjectModel(Users.name) private userModel: Model<IUser>,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly otpService: OtpService,
    private readonly mailerService: MailerService,
  ) {}

  async signUp(signUpDto: signUpDto): Promise<{ message: string; email: string }> {
    
      
      const user: Omit<IUserSignUp, 'password'> =
        await this.usersService.createUser(signUpDto);
        if (!user) {
          throw new HttpException(
            {
              status: HttpStatus.BAD_REQUEST,
              message: 'User already exists',
            },
            HttpStatus.BAD_REQUEST,
          );
        }

      // Generate and store OTP for email verification
      const otp = otpgenerator();

      await this.otpService.create({
        email: user.email,
        otp: otp.toString(),
      });

      // Send verification email
      await this.mailerService.sendMail({
        to: user.email,
        subject: 'Email Verification OTP',
        text: `Your OTP for email verification is ${otp}. This OTP will expire in 5 minutes.`,
      });

      return {
        message: `Verification OTP sent to ${user.email}. Please verify your email to complete registration.`,
        email: user.email,
      };
     
  }

  async signIn(signInDto: signInDto): Promise<IAuth> {
    const user = await this.usersService.findByEmail(
      signInDto.email.toLowerCase(),
    );
    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Check if user is verified
    if (!user.isVerified) {
      throw new HttpException(
        {
          status: HttpStatus.UNAUTHORIZED,
          message: 'Please verify your email before signing in',
          code: 'EMAIL_NOT_VERIFIED',
        },
        HttpStatus.UNAUTHORIZED,
      );
    }

    const userWithPassword = await this.usersService.findByEmailWithPassword(
      signInDto.email.toLowerCase(),
    );
    const isMatch = await bcrypt.compare(
      signInDto.password,
      userWithPassword.password,
    );
    if (!isMatch) {
      throw new Error('Invalid credentials');
    }
    const payload = { name: user.firstName || user.email, _id: user._id };
    const accessToken = await this.jwtService.signAsync(payload);
    return {
      ...user,
      accessToken,
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    try {
      const email = forgotPasswordDto.email.toLowerCase();
      const user = await this.usersService.findByEmail(email);

      if (!user) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Invalid Email',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Generate and store OTP
      const otp = otpgenerator();

      const opt = await this.otpService.create({
        email: user.email,
        otp: otp.toString(),
      });

      console.log('otp', opt);

      await this.mailerService.sendMail({
        to: email,
        subject: 'Reset Password OTP',
        text: `Your OTP for resetting password is ${otp}.`,
      });

      return { message: `OTP sent to ${email}` };
    } catch (error) {
      console.error('Error in forgotPassword:', error);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Something went wrong. Please try again later.',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDTO): Promise<any> {
    const email = verifyOtpDto.email.toLowerCase();
    const otpFound = await this.otpService.findOne(email, verifyOtpDto.otp);

    if (!otpFound) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid OTP',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    this.otpService.delete(otpFound._id as string);

    return {
      message: 'OTP Verified',
      status: 200,
    };
  }

  async verifyEmailOtp(verifyOtpDto: VerifyOtpDTO): Promise<ISignUp> {
    try {
      const email = verifyOtpDto.email.toLowerCase();
      const otpFound = await this.otpService.findOne(email, verifyOtpDto.otp);

      if (!otpFound) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Invalid or expired OTP',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Find the user
      const user = await this.usersService.findByEmail(email);
      if (!user) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'User not found',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Mark user as verified
      await this.userModel.findByIdAndUpdate(
        user._id,
        { isVerified: true },
        { new: true }
      );

      // Delete the OTP
      await this.otpService.delete(otpFound._id as string);

      // Generate access token
      const payload = { name: user.email, _id: user._id };
      const accessToken = await this.jwtService.signAsync(payload);

      return {
        _id: user._id,
        email: user.email,
        accessToken,
      };
    } catch (error) {
      console.error('Error in verifyEmailOtp:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Something went wrong during email verification. Please try again later.',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

 async resetPassword(dto: ResetPasswordDto): Promise<any> {
    // Reset password
    const hashPassword = await bcrypt.hash(dto.password, 10);
    const email = dto.email.toLowerCase();
    const result = await this.usersService.update(email, {
      password: hashPassword,
    });
    if (!result) {
      throw new Error('Update failed: User not found');
    }

    return {
      message: 'PASSWORD RESTORED - 200',
    };
  }

   async changePassword(
    userId: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    const { oldPassword, newPassword } = changePasswordDto;

    if (!oldPassword || !newPassword) {
      throw new BadRequestException(
        'Old password and new password are required',
      );
    }

    const user = await this.userModel.findById(userId).select('+password');
    if (!user) {
      throw new NotFoundException(`User not found with ID: ${userId}`);
    }

    if (!user.password) {
      throw new InternalServerErrorException(
        'Password field is missing for the user',
      );
    }

    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isOldPasswordValid) {
      throw new UnauthorizedException('Invalid old password');
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    await this.userModel.findByIdAndUpdate(
      userId,
      { password: hashedNewPassword },
      { new: true },
    );

    return { message: 'Password changed successfully' };
  }

  async resendVerificationOtp(email: string): Promise<{ message: string }> {
    try {
      const normalizedEmail = email.toLowerCase();
      const user = await this.usersService.findByEmail(normalizedEmail);

      if (!user) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'User not found',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      if (user.isVerified) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Email is already verified',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Generate and store new OTP
      const otp = otpgenerator();

      await this.otpService.create({
        email: normalizedEmail,
        otp: otp.toString(),
      });

      // Send verification email
      await this.mailerService.sendMail({
        to: normalizedEmail,
        subject: 'Email Verification OTP - Resent',
        text: `Your new OTP for email verification is ${otp}. This OTP will expire in 5 minutes.`,
      });

      return {
        message: `Verification OTP resent to ${normalizedEmail}`,
      };
    } catch (error) {
      console.error('Error in resendVerificationOtp:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Something went wrong while resending OTP. Please try again later.',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async verifyGoogleToken(idToken: string) {
    const ticket = await this.client.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();

    return {
      email: payload.email,
      name: payload.name,
      picture: payload.picture,
      googleId: payload.sub,
    };
  }

  async findOrCreateUser(googleUser) {
    console.log(googleUser);
    let user = await this.userModel.findOne({ email: googleUser.email });

    if (!user) {
      user = new this.userModel({
        email: googleUser.email,
        name: googleUser.firstName,
        gender: googleUser.gender,
        photo: googleUser.picture,
        googleId: googleUser.googleId,
        provider: 'google',
      });
      console.log("user",user)
      await user.save();
    }

    return user;
  }

  async generateJwt(user) {
    const payload = { sub: user.id, email: user.email };
    return this.jwtService.sign(payload);
  }
}


